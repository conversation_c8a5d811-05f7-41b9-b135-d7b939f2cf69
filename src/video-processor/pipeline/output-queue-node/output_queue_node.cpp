#include "./output_queue_node.h"
#include <iostream>
#include <sstream>
#include <fstream>

namespace IQVideoProcessor::Pipeline {

OutputQueueNode::OutputQueueNode(std::function<bool()> controlLambda) : controlLambda_(std::move(controlLambda)) {
  setRunning();
}

OutputQueueNode::~OutputQueueNode() {
  PipelineComponent::stop();
}

bool OutputQueueNode::process(FrameCompositionResult& result) {
  if (!running()) return false;

  writeCompositionResultToFile(result);

  if (!controlLambda_()) {
    stop();
    return false;
  }

  return running();
}

bool OutputQueueNode::hasResults() const {
  return false;
}

FrameCompositionResult& OutputQueueNode::getNextResult() {

}

void OutputQueueNode::writeCompositionResultToFile(const FrameCompositionResult& result) {
  std::ostringstream filename;
  filename << "frames/frame_" << result.frameNumber << ".jpg";

  std::ofstream file(filename.str(), std::ios::binary);
  if (!file.is_open()) {
    std::cerr << "OutputQueueNode: Failed to open output file: " << filename.str() << std::endl;
    return;
  }

  file.write(reinterpret_cast<const char*>(&result.data[0]), result.dataSize);
  const bool writeSuccess = file.good();
  file.close();

  if (!writeSuccess) {
    std::cerr << "OutputQueueNode: Failed to write JPEG data to file: " << filename.str() << std::endl;
  } else {
    std::cout << "OutputQueueNode: Successfully wrote frame " << result.frameNumber << " to " << filename.str() << " (" << result.dataSize << " bytes)" << std::endl;
  }
}

} // namespace IQVideoProcessor::Pipeline
