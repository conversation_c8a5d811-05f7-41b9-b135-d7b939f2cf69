#pragma once
#include "../frame_composition_node_types.h"
#include "../../../stream-pipeline/stream_node.h"
#include <functional>

namespace IQVideoProcessor::Pipeline {

class OutputQueueNode final : public SPipeline::StreamNode<FrameCompositionResult, FrameCompositionResult> {
public:
  explicit OutputQueueNode(std::function<bool()> controlLambda);
  ~OutputQueueNode() override;

  [[nodiscard]] bool hasResults() const;
  [[nodiscard]] FrameCompositionResult& getNextResult();

private:
  bool process(FrameCompositionResult& result) override;

  std::function<bool()> controlLambda_;

  // DEBUG helper to write frame to file
  static void writeCompositionResultToFile(const FrameCompositionResult& result) ;
};

} // namespace IQVideoProcessor::Pipeline
