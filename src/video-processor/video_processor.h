#pragma once

#include "../iiq-stream/iiq_stream.h"
#include "./pipeline/pipeline.h"
#include <memory>
#include <functional>
#include <thread>

namespace IQVideoProcessor {

class VideoProcessor {
public:
  explicit VideoProcessor(std::unique_ptr<IIQStream> stream, std::function<void()> onHasResults);
  ~VideoProcessor();

  bool initialize();
  void shutdown();
  void start(); // New method for starting worker thread

private:
  std::unique_ptr<IIQStream> stream_;
  std::function<void()> onHasResults_;
  SampleRateType sampleRate_{0}; // Sample rate of the video stream
  size_t maxVideoLineSamples_{0}; // Maximum samples per video line

  std::atomic<bool> initialized_{false};

  // Pipeline components (pointer-based storage)
  std::unique_ptr<Pipeline::IQAcquisitionNode> acquisitionNode_;
  std::unique_ptr<Pipeline::IQAcquisitionBridge> acquisitionBridge_;
  std::unique_ptr<Pipeline::IQDemodulationNode> demodNode_;
  std::unique_ptr<Pipeline::IQDemodulationLink> demodPassthrough_;
  std::unique_ptr<Pipeline::LineDetectionNode> lineDetectionNode_;
  std::unique_ptr<Pipeline::LineDetectionLink> lineDetectionPassthrough_;
  std::unique_ptr<Pipeline::FrameCompositionNode> frameCompositionNode_;
  std::unique_ptr<Pipeline::FrameCompositionLink> frameCompositionPassthrough_;
  std::unique_ptr<Pipeline::OutputQueueNode> outputQueueNode_;

  // Worker thread
  std::unique_ptr<std::thread> workerThread_;

  void connectPipelineNodes();
};

} // namespace IQVideoProcessor
