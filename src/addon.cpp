#include <node.h>
#include <v8.h>
#include <memory>
#include <iostream>
#include <thread>
#include "wav-stream/wav_stream.h"
#include "video-processor/video_processor.h"

namespace VideoDecodingAddon {

using v8::Context;
using v8::Function;
using v8::FunctionCallbackInfo;
using v8::Isolate;
using v8::Local;
using v8::Object;
using v8::String;
using v8::Value;
using v8::Boolean;
using v8::Exception;

// Single global VideoProcessor instance (simplified single-instance design)
static std::unique_ptr<IQVideoProcessor::VideoProcessor> g_videoProcessor;

void CreateIQVideoProcessor(const FunctionCallbackInfo<Value>& args) {
    Isolate* isolate = args.GetIsolate();

    try {
        // Stop any existing processor (single-instance design)
        if (g_videoProcessor) {
            g_videoProcessor.reset();
        }

        // Create WAV stream data source
        // auto wavStream = std::make_unique<WAVIQStream>("samples/spectrozir_6.5mhz.wav", true, true);
        auto wavStream = std::make_unique<WAVIQStream>("samples/recording.wav", true, true);
        // auto wavStream = std::make_unique<WAVIQStream>("samples/bladerf_2.wav", true, true);

        // Open the WAV file
        if (!wavStream->open()) {
            std::string error = "Failed to open WAV file: " + wavStream->lastError();
            isolate->ThrowException(Exception::Error(String::NewFromUtf8(isolate, error.c_str()).ToLocalChecked()));
            return;
        }

        // Create VideoProcessor instance
        g_videoProcessor = std::make_unique<IQVideoProcessor::VideoProcessor>(std::move(wavStream), []() {
            // Callback when a new frame is ready (can be expanded for more functionality)
            std::cout << "New frame ready callback invoked." << std::endl;
        });

        // Initialize the VideoProcessor
        if (!g_videoProcessor->initialize()) {
            isolate->ThrowException(Exception::Error(String::NewFromUtf8(isolate, "Failed to initialize VideoProcessor").ToLocalChecked()));
            return;
        }

        // Start the VideoProcessor (this will create and join worker thread)
        g_videoProcessor->start();

        args.GetReturnValue().Set(Boolean::New(isolate, true));

    } catch (const std::exception& e) {
        g_videoProcessor.reset();
        isolate->ThrowException(Exception::Error(String::NewFromUtf8(isolate, ("VideoProcessor error: " + std::string(e.what())).c_str()).ToLocalChecked()));
    }
}

void StopIQVideoProcessor(const FunctionCallbackInfo<Value>& args) {
    Isolate* isolate = args.GetIsolate();
    if (g_videoProcessor) {
        g_videoProcessor.reset();
        args.GetReturnValue().Set(Boolean::New(isolate, true));
    } else {
        args.GetReturnValue().Set(Boolean::New(isolate, false));
    }
}

/**
 * Hello World function for testing
 */
void HelloWorld(const FunctionCallbackInfo<Value>& args) {
    Isolate* isolate = args.GetIsolate();
    args.GetReturnValue().Set(String::NewFromUtf8(isolate, "Hello from Minimal Video Processor!").ToLocalChecked());
}

/**
 * Initialize the addon
 */
void Initialize(Local<Object> exports) {
    NODE_SET_METHOD(exports, "helloWorld", HelloWorld);
    NODE_SET_METHOD(exports, "createIQVideoProcessor", CreateIQVideoProcessor);
    NODE_SET_METHOD(exports, "stopIQVideoProcessor", StopIQVideoProcessor);
}

NODE_MODULE(NODE_GYP_MODULE_NAME, Initialize)

}
